-- DROP SCHEMA coca_db_test;

CREATE SCHEMA coca_db_test;
-- [backend-db-test].coca_db_test.DATABASECHANGELOG definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.DATABASECHANGELOG;

CREATE TABLE [backend-db-test].coca_db_test.DATABASECHANGELOG (
	ID nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	AUTHOR nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	FILENAME nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	DATEEXECUTED datetime2(3) NOT NULL,
	ORDEREXECUTED int NOT NULL,
	EXECTYPE nvarchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	MD5SU<PERSON> nvarchar(35) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	DESCRIPTION nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	COMMENTS nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	TAG nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	LIQUIBASE nvarchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	CONTEXTS nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	LABELS nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	DEPLOYMENT_ID nvarchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL
);


-- [backend-db-test].coca_db_test.DATABASECHANGELOGLOCK definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.DATABASECHANGELOGLOCK;

CREATE TABLE [backend-db-test].coca_db_test.DATABASECHANGELOGLOCK (
	ID int NOT NULL,
	LOCKED bit NOT NULL,
	LOCKGRANTED datetime2(3) NULL,
	LOCKEDBY nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	CONSTRAINT PK_DATABASECHANGELOGLOCK PRIMARY KEY (ID)
);


-- [backend-db-test].coca_db_test.clusters definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.clusters;

CREATE TABLE [backend-db-test].coca_db_test.clusters (
	id bigint IDENTITY(1,1) NOT NULL,
	name varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	[type] int DEFAULT 0 NOT NULL,
	CONSTRAINT PK_CLUSTERS PRIMARY KEY (id),
	CONSTRAINT uc_clusters_name_type UNIQUE (name,[type])
);
 CREATE NONCLUSTERED INDEX idx_clusters ON backend-db-test.coca_db_test.clusters (  name ASC  )
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;


-- [backend-db-test].coca_db_test.column_escalation_values definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.column_escalation_values;

CREATE TABLE [backend-db-test].coca_db_test.column_escalation_values (
	id bigint IDENTITY(1,1) NOT NULL,
	labour_escalation_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	v25_royalties_escalation_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_COLUMN_ESCALATION_VALUES PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.conditional_tasks definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.conditional_tasks;

CREATE TABLE [backend-db-test].coca_db_test.conditional_tasks (
	id bigint IDENTITY(1,1) NOT NULL,
	currency varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	conditional_task varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_CONDITIONAL_TASKS PRIMARY KEY (id),
	CONSTRAINT uc_conditional_tasks_currency_year UNIQUE (currency,[year])
);


-- [backend-db-test].coca_db_test.customers definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.customers;

CREATE TABLE [backend-db-test].coca_db_test.customers (
	id bigint IDENTITY(1,1) NOT NULL,
	three_letter_code varchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[type] varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	name varchar(70) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	number varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	CONSTRAINT PK_CUSTOMERS PRIMARY KEY (id),
	CONSTRAINT uc_customers_three_letter_code_type_name_number UNIQUE (three_letter_code,[type],name,number)
);


-- [backend-db-test].coca_db_test.data_migrations definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.data_migrations;

CREATE TABLE [backend-db-test].coca_db_test.data_migrations (
	id bigint IDENTITY(1,1) NOT NULL,
	file_name varchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	file_md5sum varchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_DATA_MIGRATIONS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.discount_constants definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.discount_constants;

CREATE TABLE [backend-db-test].coca_db_test.discount_constants (
	id bigint IDENTITY(1,1) NOT NULL,
	expendable_level_zero varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	expendable_level_one varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	expendable_level_two varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	volume_based_baseline_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	volume_based_scaling_factor_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_DISCOUNT_CONSTANTS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.engines definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.engines;

CREATE TABLE [backend-db-test].coca_db_test.engines (
	id bigint IDENTITY(1,1) NOT NULL,
	name varchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_ENGINES PRIMARY KEY (id),
	CONSTRAINT UQ__engines__72E12F1BA3077230 UNIQUE (name)
);


-- [backend-db-test].coca_db_test.exchange_rates definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.exchange_rates;

CREATE TABLE [backend-db-test].coca_db_test.exchange_rates (
	id bigint IDENTITY(1,1) NOT NULL,
	value decimal(15,8) NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_EXCHANGE_RATES PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.handling_charges definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.handling_charges;

CREATE TABLE [backend-db-test].coca_db_test.handling_charges (
	id bigint DEFAULT NEXT VALUE FOR [handling_charges_seq] NOT NULL,
	z1_margin_percentage decimal(4,3) NULL,
	usm_margin_percentage decimal(4,3) NULL,
	pma_margin_percentage decimal(4,3) NULL,
	csm_margin_percentage decimal(4,3) NULL,
	one_item_cap int NULL,
	line_item_cap int NULL,
	material_pricing_item_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_HANDLING_CHARGES PRIMARY KEY (id),
	CONSTRAINT UQ__handling__14C54F092E0DE4CE UNIQUE (material_pricing_item_id)
);


-- [backend-db-test].coca_db_test.hurdles definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.hurdles;

CREATE TABLE [backend-db-test].coca_db_test.hurdles (
	id int NULL,
	hurdle_ens_percentage real NULL,
	hurdle_mes_percentage real NULL,
	created_at nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	updated_at nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	[year] int NULL
);


-- [backend-db-test].coca_db_test.import_lock definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.import_lock;

CREATE TABLE [backend-db-test].coca_db_test.import_lock (
	id bigint IDENTITY(1,1) NOT NULL,
	is_locked bit DEFAULT 0 NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_IMPORT_LOCK PRIMARY KEY (id),
	CONSTRAINT UQ__import_l__BB70B1A5111E1914 UNIQUE (is_locked)
);


-- [backend-db-test].coca_db_test.leap_licence_fees definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.leap_licence_fees;

CREATE TABLE [backend-db-test].coca_db_test.leap_licence_fees (
	id bigint IDENTITY(1,1) NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	currency varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ws_a_leap1a_cbsa_fee varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ws_b_leap1a_cbsa_fee varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ws_a_leap1b_cbsa_fee varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ws_b_leap1b_cbsa_fee varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_LEAP_LICENCE_FEES PRIMARY KEY (id),
	CONSTRAINT UQ__leap_lic__809A238B3CD24379 UNIQUE ([year])
);


-- [backend-db-test].coca_db_test.leap_utilization_fees definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.leap_utilization_fees;

CREATE TABLE [backend-db-test].coca_db_test.leap_utilization_fees (
	id bigint IDENTITY(1,1) NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	currency varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ws_a_utilization_fee varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ws_b_utilization_fee varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_LEAP_UTILIZATION_FEES PRIMARY KEY (id),
	CONSTRAINT UQ__leap_uti__809A238B29F4D7DC UNIQUE ([year])
);


-- [backend-db-test].coca_db_test.navigation_steps definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.navigation_steps;

CREATE TABLE [backend-db-test].coca_db_test.navigation_steps (
	id bigint IDENTITY(1,1) NOT NULL,
	name varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_NAVIGATION_STEPS PRIMARY KEY (id),
	CONSTRAINT UQ__navigati__72E12F1B027F3DAD UNIQUE (name)
);


-- [backend-db-test].coca_db_test.operation_states definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.operation_states;

CREATE TABLE [backend-db-test].coca_db_test.operation_states (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	[type] varchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	is_completed bit DEFAULT 0 NOT NULL,
	progress_state varchar(11) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	is_locked bit DEFAULT 0 NOT NULL,
	locked_at datetime2 DEFAULT getdate() NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_OPERATION_STATES PRIMARY KEY (id),
	CONSTRAINT uc_quotation_id_type UNIQUE (quotation_id,[type])
);


-- [backend-db-test].coca_db_test.overheads definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.overheads;

CREATE TABLE [backend-db-test].coca_db_test.overheads (
	id bigint IDENTITY(1,1) NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	customer_type varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ebit_overhead_ebit_sc_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ebit_overhead_d3_sc_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	workscope_class varchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT 'A' NOT NULL,
	ebit_overhead decimal(20,8) DEFAULT 0 NOT NULL,
	db3_overhead decimal(20,8) DEFAULT 0 NOT NULL,
	CONSTRAINT PK_OVERHEADS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.parts definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.parts;

CREATE TABLE [backend-db-test].coca_db_test.parts (
	id bigint IDENTITY(1,1) NOT NULL,
	name varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	[type] varchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT '' NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_PARTS PRIMARY KEY (id),
	CONSTRAINT uc_parts_name_type UNIQUE (name,[type])
);
 CREATE NONCLUSTERED INDEX idx_parts_type_name ON backend-db-test.coca_db_test.parts (  type ASC  , name ASC  )
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;


-- [backend-db-test].coca_db_test.progress_steps definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.progress_steps;

CREATE TABLE [backend-db-test].coca_db_test.progress_steps (
	id bigint IDENTITY(1,1) NOT NULL,
	name varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_PROGRESS_STEPS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.repairs definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.repairs;

CREATE TABLE [backend-db-test].coca_db_test.repairs (
	id bigint IDENTITY(1,1) NOT NULL,
	name nvarchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_REPAIRS PRIMARY KEY (id)
);
 CREATE NONCLUSTERED INDEX idx_repairs_name ON backend-db-test.coca_db_test.repairs (  name ASC  )
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;


-- [backend-db-test].coca_db_test.subcontract_discount_conditions definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.subcontract_discount_conditions;

CREATE TABLE [backend-db-test].coca_db_test.subcontract_discount_conditions (
	id bigint IDENTITY(1,1) NOT NULL,
	subcontractor varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	discount_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_SUBCONTRACT_DISCOUNT_CONDITIONS PRIMARY KEY (id),
	CONSTRAINT subcontract_discount_conditions_subcontractor_year UNIQUE (subcontractor,[year])
);


-- [backend-db-test].coca_db_test.subcontract_discount_portions definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.subcontract_discount_portions;

CREATE TABLE [backend-db-test].coca_db_test.subcontract_discount_portions (
	id int NULL,
	engine_id int NULL,
	ge_percentage int NULL,
	hpt_blade_ge_percentage int NULL,
	pw_percentage int NULL,
	chromalloy_percentage int NULL,
	ltts_53_percentage int NULL,
	leap_1a_percentage real NULL,
	mtu_percentage int NULL,
	schaffler_percentage int NULL,
	standard_aero_percentage int NULL,
	created_at nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	updated_at nvarchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ltts_62_percentage int NULL,
	ltts_82_7_percentage int NULL,
	part_id int NULL,
	ltts_55_percentage int NULL,
	ltts_60_8_percentage int NULL,
	ltts_66_25_percentage int NULL,
	ltts_70_7_percentage int NULL
);


-- [backend-db-test].coca_db_test.surcharges definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.surcharges;

CREATE TABLE [backend-db-test].coca_db_test.surcharges (
	id bigint IDENTITY(1,1) NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	labour_cost_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ah_rate_v25_cfm56_cf6_leap varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	ah_rate_v25_cfm56_cf6_leap_yoy_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	tec_surcharge_per_ws_a_inh_standard varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	tec_surcharge_per_ws_a_inh_leap_cfm_oem_offload varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	tec_surcharge_per_ws_a_sc_dlh_swr_pw1_ovh varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	tec_surcharge_enl_per_month_per_lease_event varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	tec_surcharge_per_ws_b_fixed_part_inh_sc_oem_offloads varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	tec_surcharge_per_ws_b_c_variable_part_inh_sc_oem_offloads_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	teo21_22_surcharge_per_ws_a_b varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	teo21_22_surcharge_per_mes_hub_module_event_ham varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	internal_logistic_ws_a varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	internal_logistic_ws_b varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	internal_logistic_ws_c varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	loss_of_value_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_SURCHARGES PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.tasks definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.tasks;

CREATE TABLE [backend-db-test].coca_db_test.tasks (
	id bigint IDENTITY(1,1) NOT NULL,
	name varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_TASKS PRIMARY KEY (id)
);
 CREATE NONCLUSTERED INDEX idx_tasks ON backend-db-test.coca_db_test.tasks (  name ASC  )
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;


-- [backend-db-test].coca_db_test.taxes definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.taxes;

CREATE TABLE [backend-db-test].coca_db_test.taxes (
	id bigint IDENTITY(1,1) NOT NULL,
	earnings_tax_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_TAXES PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.testrun_items definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.testrun_items;

CREATE TABLE [backend-db-test].coca_db_test.testrun_items (
	id bigint IDENTITY(1,1) NOT NULL,
	dbii_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	quotation_engine_id bigint NOT NULL,
	cluster_id bigint NOT NULL,
	handling_charge_id bigint NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_TESTRUN_ITEMS PRIMARY KEY (id),
	CONSTRAINT uc_testrun_items_quotation_engine_id_cluster_id UNIQUE (quotation_engine_id,cluster_id)
);


-- [backend-db-test].coca_db_test.third_party_credits definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.third_party_credits;

CREATE TABLE [backend-db-test].coca_db_test.third_party_credits (
	id bigint IDENTITY(1,1) NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	discount_percentage decimal(20,8) NOT NULL,
	third_party_cap decimal(20,8) NOT NULL,
	third_party_moderate decimal(20,8) NOT NULL,
	third_party_harsh decimal(20,8) NOT NULL,
	third_party_supplemental decimal(20,8) NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_THIRD_PARTY_CREDITS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.users definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.users;

CREATE TABLE [backend-db-test].coca_db_test.users (
	id bigint IDENTITY(1,1) NOT NULL,
	name varchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	email varchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	u_number varchar(7) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	sap_username varchar(12) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_USERS PRIMARY KEY (id),
	CONSTRAINT UQ__users__D3DC12254A0C3FE9 UNIQUE (u_number),
	CONSTRAINT UQ__users__E1EA2A430BCF7A97 UNIQUE (sap_username),
	CONSTRAINT uc_users_name_u_number UNIQUE (name,u_number)
);


-- [backend-db-test].coca_db_test.wacc definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.wacc;

CREATE TABLE [backend-db-test].coca_db_test.wacc (
	id bigint IDENTITY(1,1) NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	wacc_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_WACC PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.workscopes definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.workscopes;

CREATE TABLE [backend-db-test].coca_db_test.workscopes (
	id bigint IDENTITY(1,1) NOT NULL,
	name varchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	is_system bit NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_WORKSCOPES PRIMARY KEY (id),
	CONSTRAINT UQ__workscop__72E12F1B47768419 UNIQUE (name)
);
 CREATE NONCLUSTERED INDEX idx_workscopes ON backend-db-test.coca_db_test.workscopes (  is_system ASC  )
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;


-- [backend-db-test].coca_db_test.commitment_letter_discounts definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.commitment_letter_discounts;

CREATE TABLE [backend-db-test].coca_db_test.commitment_letter_discounts (
	id bigint IDENTITY(1,1) NOT NULL,
	engine_id bigint NOT NULL,
	non_llp_tiered_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	non_llp_escalated_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	llp_tiered_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	llp_escalated_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT '2024' NOT NULL,
	CONSTRAINT PK_COMMITMENT_LETTER_DISCOUNTS PRIMARY KEY (id),
	CONSTRAINT uc_year_engine_id UNIQUE (engine_id,[year]),
	CONSTRAINT fk_commitment_letter_discounts_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id)
);


-- [backend-db-test].coca_db_test.engine_clusters definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.engine_clusters;

CREATE TABLE [backend-db-test].coca_db_test.engine_clusters (
	id bigint IDENTITY(1,1) NOT NULL,
	sort_order int NULL,
	engine_id bigint NOT NULL,
	cluster_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_ENGINE_CLUSTERS PRIMARY KEY (id),
	CONSTRAINT uc_engine_clusters_engine_id_cluster_id UNIQUE (engine_id,cluster_id),
	CONSTRAINT fk_engine_clusters_clusters FOREIGN KEY (cluster_id) REFERENCES [backend-db-test].coca_db_test.clusters(id),
	CONSTRAINT fk_engine_clusters_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id)
);


-- [backend-db-test].coca_db_test.engine_modules definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.engine_modules;

CREATE TABLE [backend-db-test].coca_db_test.engine_modules (
	id bigint IDENTITY(1,1) NOT NULL,
	engine_id bigint NOT NULL,
	module_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_ENGINE_MODULES PRIMARY KEY (id),
	CONSTRAINT uc_engine_modules_engine_id_module_id UNIQUE (engine_id,module_id),
	CONSTRAINT fk_engine_modules_clusters FOREIGN KEY (module_id) REFERENCES [backend-db-test].coca_db_test.clusters(id),
	CONSTRAINT fk_engine_modules_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id)
);


-- [backend-db-test].coca_db_test.epar_prices definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.epar_prices;

CREATE TABLE [backend-db-test].coca_db_test.epar_prices (
	id bigint IDENTITY(1,1) NOT NULL,
	cleaning_and_inspection varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	repair varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	currency varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	engine_version varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	engine_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_EPAR_PRICES PRIMARY KEY (id),
	CONSTRAINT uc_epar_prices_year_engine_version_engine_id_currency UNIQUE ([year],engine_version,engine_id,currency),
	CONSTRAINT fk_epar_prices_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id)
);


-- [backend-db-test].coca_db_test.escalations_prices_defaults definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.escalations_prices_defaults;

CREATE TABLE [backend-db-test].coca_db_test.escalations_prices_defaults (
	engine_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	labour_prices_percentage decimal(5,4) NOT NULL,
	epar_prices_percentage decimal(5,4) NOT NULL,
	rfp_labour_percentage decimal(5,4) NULL,
	material_prices_percentage decimal(5,4) NOT NULL,
	subcontract_prices_percentage decimal(5,4) NOT NULL,
	id bigint IDENTITY(1,1) NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK__escalati__3213E83F592F2426 PRIMARY KEY (id),
	CONSTRAINT uc_engine_id_year UNIQUE (engine_id,[year]),
	CONSTRAINT fk_escalations_material_prices_defaults_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id)
);


-- [backend-db-test].coca_db_test.expendable_material_discounts definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.expendable_material_discounts;

CREATE TABLE [backend-db-test].coca_db_test.expendable_material_discounts (
	id bigint IDENTITY(1,1) NOT NULL,
	engine_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	level_zero_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	level_one_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	level_two_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_EXPENDABLE_MATERIAL_DISCOUNTS PRIMARY KEY (id),
	CONSTRAINT expendable_material_discounts_engine_id_year UNIQUE (engine_id,[year]),
	CONSTRAINT fk_expendable_material_discounts_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id)
);


-- [backend-db-test].coca_db_test.material_discount_conditions definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.material_discount_conditions;

CREATE TABLE [backend-db-test].coca_db_test.material_discount_conditions (
	id bigint IDENTITY(1,1) NOT NULL,
	engine_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	non_llp_tiered_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	non_llp_escalated_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	llp_tiered_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	llp_escalated_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_MATERIAL_DISCOUNT_CONDITIONS PRIMARY KEY (id),
	CONSTRAINT material_discount_conditions_engine_id_year UNIQUE (engine_id,[year]),
	CONSTRAINT fk_material_discount_conditions_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id)
);


-- [backend-db-test].coca_db_test.material_discount_portions definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.material_discount_portions;

CREATE TABLE [backend-db-test].coca_db_test.material_discount_portions (
	id bigint IDENTITY(1,1) NOT NULL,
	engine_id bigint NOT NULL,
	labour_type varchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	part_type varchar(25) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	expendable_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	non_expendable_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	llp_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	is_expendable_editable bit DEFAULT 0 NOT NULL,
	is_non_expendable_editable bit DEFAULT 0 NOT NULL,
	is_llp_editable bit DEFAULT 0 NOT NULL,
	CONSTRAINT PK_MATERIAL_DISCOUNT_PORTIONS PRIMARY KEY (id),
	CONSTRAINT material_discount_portions_engine_id_labour_type_part_type UNIQUE (engine_id,labour_type,part_type),
	CONSTRAINT fk_material_discount_portions_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id)
);


-- [backend-db-test].coca_db_test.v2500_royalty_fees definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.v2500_royalty_fees;

CREATE TABLE [backend-db-test].coca_db_test.v2500_royalty_fees (
	id bigint IDENTITY(1,1) NOT NULL,
	royalty_fee_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	engine_version varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	part_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_V2500_ROYALTY_FEES PRIMARY KEY (id),
	CONSTRAINT fk_royalty_fees_parts FOREIGN KEY (part_id) REFERENCES [backend-db-test].coca_db_test.parts(id)
);


-- [backend-db-test].coca_db_test.volume_based_material_discounts definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.volume_based_material_discounts;

CREATE TABLE [backend-db-test].coca_db_test.volume_based_material_discounts (
	id bigint IDENTITY(1,1) NOT NULL,
	engine_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	target_material_cost varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	minimum_material_cost varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	maximum_volume_based_discount varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_VOLUME_BASED_MATERIAL_DISCOUNTS PRIMARY KEY (id),
	CONSTRAINT volume_based_material_discounts_engine_id_year UNIQUE (engine_id,[year]),
	CONSTRAINT fk_volume_based_material_discounts_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id)
);


-- [backend-db-test].coca_db_test.engine_cluster_parts definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.engine_cluster_parts;

CREATE TABLE [backend-db-test].coca_db_test.engine_cluster_parts (
	id bigint IDENTITY(1,1) NOT NULL,
	sort_order int NULL,
	engine_cluster_id bigint NOT NULL,
	part_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_ENGINE_CLUSTER_PARTS PRIMARY KEY (id),
	CONSTRAINT uc_engine_cluster_parts_engine_cluster_id_part_id UNIQUE (engine_cluster_id,part_id),
	CONSTRAINT fk_engine_cluster_parts_engine_clusters FOREIGN KEY (engine_cluster_id) REFERENCES [backend-db-test].coca_db_test.engine_clusters(id),
	CONSTRAINT fk_engine_cluster_parts_parts FOREIGN KEY (part_id) REFERENCES [backend-db-test].coca_db_test.parts(id)
);


-- [backend-db-test].coca_db_test.engine_cluster_tasks definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.engine_cluster_tasks;

CREATE TABLE [backend-db-test].coca_db_test.engine_cluster_tasks (
	id bigint IDENTITY(1,1) NOT NULL,
	engine_cluster_id bigint NOT NULL,
	task_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_ENGINE_CLUSTER_TASKS PRIMARY KEY (id),
	CONSTRAINT uc_engine_cluster_tasks_engine_cluster_id_task_id UNIQUE (engine_cluster_id,task_id),
	CONSTRAINT fk_engine_cluster_tasks_engine_clusters FOREIGN KEY (engine_cluster_id) REFERENCES [backend-db-test].coca_db_test.engine_clusters(id),
	CONSTRAINT fk_engine_cluster_tasks_tasks FOREIGN KEY (task_id) REFERENCES [backend-db-test].coca_db_test.tasks(id)
);


-- [backend-db-test].coca_db_test.calculation_items definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.calculation_items;

CREATE TABLE [backend-db-test].coca_db_test.calculation_items (
	id bigint DEFAULT NEXT VALUE FOR [calculation_items_seq] NOT NULL,
	item_counter int NULL,
	beschreibung1_se varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	itemtyp1_se varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	beschreibung2_se varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	itemtyp2_se varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	beschreibung3_se varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	itemtyp3_se varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	beschreibung4_se varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	itemtyp4_se varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	beschreibung5_se varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	itemtyp5_se varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	beschreibung6_se varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	itemtyp6_se varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	beschreibung7_se varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	itemtyp7_se varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	beschreibung8_se varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	itemtyp8_se varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	itemno_se int NULL,
	eingriff varchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	menge int NULL,
	beschreibung1_we varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	beschreibung2_we varchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	beschreibung3_we nvarchar(255) COLLATE SQL_Latin1_General_CP1_CS_AS NULL,
	itemtyp_last_we varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	routineflag varchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	jahr varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	itemno int NULL,
	kostenart varchar(2) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ah decimal(6,2) NULL,
	wert decimal(12,2) NULL,
	waers varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	gewmenge decimal(5,4) NULL,
	z2rate decimal(4,1) NULL,
	z3rate decimal(4,1) NULL,
	z4rate decimal(4,1) NULL,
	exprate decimal(4,1) NULL,
	verteilung decimal(4,1) NULL,
	preis_oem_z1 decimal(12,2) NULL,
	waers_oem varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ef varchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	pt varchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	rq decimal(5,2) NULL,
	rt varchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	vt varchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	z2formula nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	z3formula nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	z4formula nvarchar(255) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	ep decimal(8,2) NULL,
	CONSTRAINT PK_CALCULATION_ITEMS PRIMARY KEY (id),
	CONSTRAINT UQ__calculat__3213E83E219B4179 UNIQUE (id)
);
 CREATE NONCLUSTERED INDEX idx_calculation_items_quotation_id_kostenart ON backend-db-test.coca_db_test.calculation_items (  quotation_id ASC  , kostenart ASC  )
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;


-- [backend-db-test].coca_db_test.cleaning_and_inspections definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.cleaning_and_inspections;

CREATE TABLE [backend-db-test].coca_db_test.cleaning_and_inspections (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	is_rlfp_engine_included bit DEFAULT 0 NOT NULL,
	is_rlfp_module_included bit DEFAULT 0 NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_CLEANING_AND_INSPECTIONS PRIMARY KEY (id),
	CONSTRAINT UQ__cleaning__7841D7DAED949B35 UNIQUE (quotation_id)
);


-- [backend-db-test].coca_db_test.clp_thresholds definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.clp_thresholds;

CREATE TABLE [backend-db-test].coca_db_test.clp_thresholds (
	id bigint IDENTITY(1,1) NOT NULL,
	clp_threshold bigint NULL,
	quotation_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_CLP_THRESHOLDS PRIMARY KEY (id),
	CONSTRAINT uq_clp_thresholds_quotation_id UNIQUE (quotation_id)
);


-- [backend-db-test].coca_db_test.comments definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.comments;

CREATE TABLE [backend-db-test].coca_db_test.comments (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	content varchar(150) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_COMMENTS PRIMARY KEY (id),
	CONSTRAINT UQ__comments__7841D7DA9F71C43E UNIQUE (quotation_id)
);


-- [backend-db-test].coca_db_test.commitment_letter_parts definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.commitment_letter_parts;

CREATE TABLE [backend-db-test].coca_db_test.commitment_letter_parts (
	id bigint IDENTITY(1,1) NOT NULL,
	part_id bigint NOT NULL,
	quotation_id bigint NOT NULL,
	has_commitment_letter bit DEFAULT 0 NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_COMMITMENT_LETTER_PARTS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.contract_types definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.contract_types;

CREATE TABLE [backend-db-test].coca_db_test.contract_types (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	time_and_material bit DEFAULT 1 NOT NULL,
	routine_fixed_prices bit DEFAULT 1 NOT NULL,
	workscope_nte bit DEFAULT 0 NOT NULL,
	workscope_fixed_price bit DEFAULT 0 NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	modular_nte bit DEFAULT 0 NOT NULL,
	modular_fixed_price bit DEFAULT 0 NOT NULL,
	CONSTRAINT PK_CONTRACT_TYPES PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.discounts definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.discounts;

CREATE TABLE [backend-db-test].coca_db_test.discounts (
	id bigint DEFAULT NEXT VALUE FOR [discounts_seq] NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	currency varchar(10) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	material_a_part_expendable decimal(20,8) NULL,
	material_a_part_non_expendable decimal(20,8) NULL,
	material_case_and_frame_expendable decimal(20,8) NULL,
	material_case_and_frame_non_expendable decimal(20,8) NULL,
	material_llp decimal(20,8) NULL,
	material_parts_package_expendable decimal(20,8) NULL,
	material_parts_package_non_expendable decimal(20,8) NULL,
	material_kit_expendable decimal(20,8) NULL,
	material_kit_non_expendable decimal(20,8) NULL,
	material_non_routine_a_part_expendable decimal(20,8) NULL,
	material_non_routine_a_part_non_expendable decimal(20,8) NULL,
	material_non_routine_case_and_frame_expendable decimal(20,8) NULL,
	material_non_routine_case_and_frame_non_expendable decimal(20,8) NULL,
	material_non_routine_llp_expendable decimal(20,8) NULL,
	material_non_routine_llp_non_expendable decimal(20,8) NULL,
	material_routine_expendable decimal(20,8) NULL,
	material_volume_based decimal(20,8) NULL,
	subcontract_a_part decimal(20,8) NULL,
	subcontract_case_and_frame decimal(20,8) NULL,
	subcontract_llp decimal(20,8) NULL,
	subcontract_parts_package decimal(20,8) NULL,
	subcontract_component decimal(20,8) NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	calculation_status varchar(8) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT '' NOT NULL,
	CONSTRAINT PK_DISCOUNTS PRIMARY KEY (id),
	CONSTRAINT uc_discounts_quotation_id_year_workscope_id_currency_calculation_status UNIQUE (quotation_id,[year],workscope_id,currency,calculation_status)
);


-- [backend-db-test].coca_db_test.escalations definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.escalations;

CREATE TABLE [backend-db-test].coca_db_test.escalations (
	id bigint DEFAULT NEXT VALUE FOR [escalations_seq] NOT NULL,
	quotation_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	labour_prices_percentage decimal(5,4) DEFAULT 0.0 NOT NULL,
	epar_prices_percentage decimal(5,4) DEFAULT 5.0 NOT NULL,
	rfp_labour_percentage decimal(5,4) NULL,
	hc_material_prices_percentage decimal(5,4) NOT NULL,
	hc_subcontract_prices_percentage decimal(5,4) NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	fp_nte_prices_percentage decimal(5,4) NULL,
	material_escalation_cost_percentage decimal(5,4) NULL,
	CONSTRAINT PK_ESCALATIONS PRIMARY KEY (id),
	CONSTRAINT uc_escalations_year_quotation_id UNIQUE ([year],quotation_id)
);


-- [backend-db-test].coca_db_test.fp_nte_prices_escalations definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.fp_nte_prices_escalations;

CREATE TABLE [backend-db-test].coca_db_test.fp_nte_prices_escalations (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	material_percentage decimal(5,4) NULL,
	labour_percentage decimal(5,4) NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_WORKSCOPE_PRICES_ESCALATIONS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.handling_charge_global_caps definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.handling_charge_global_caps;

CREATE TABLE [backend-db-test].coca_db_test.handling_charge_global_caps (
	id bigint IDENTITY(1,1) NOT NULL,
	hc_material_global_cap int NULL,
	hc_subcontract_global_cap int NULL,
	quotation_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_HANDLING_CHARGE_GLOBAL_CAPS PRIMARY KEY (id),
	CONSTRAINT UQ__handling__7841D7DA0B60A557 UNIQUE (quotation_id)
);


-- [backend-db-test].coca_db_test.labour_pricing_items definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.labour_pricing_items;

CREATE TABLE [backend-db-test].coca_db_test.labour_pricing_items (
	id bigint DEFAULT NEXT VALUE FOR [labour_pricing_items_seq] NOT NULL,
	quotation_engine_id bigint NOT NULL,
	cluster_id bigint NOT NULL,
	[type] varchar(11) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT '' NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_LABOUR_PRICING_ITEMS PRIMARY KEY (id),
	CONSTRAINT uc_labour_pricing_items_engine_id_cluster_id UNIQUE (quotation_engine_id,cluster_id)
);


-- [backend-db-test].coca_db_test.labour_rates definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.labour_rates;

CREATE TABLE [backend-db-test].coca_db_test.labour_rates (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	routine_labour_rate int NULL,
	non_routine_labour_rate int NULL,
	epar_discount_percentage decimal(4,3) NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_LABOUR_RATES PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.material_pricing_items definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.material_pricing_items;

CREATE TABLE [backend-db-test].coca_db_test.material_pricing_items (
	id bigint DEFAULT NEXT VALUE FOR [material_pricing_items_seq] NOT NULL,
	quotation_engine_id bigint NOT NULL,
	cluster_id bigint NOT NULL,
	part_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_MATERIAL_PRICING_ITEMS PRIMARY KEY (id),
	CONSTRAINT uc_material_pricing_items_quotation_engine_id_cluster_id_part_id UNIQUE (quotation_engine_id,cluster_id,part_id)
);


-- [backend-db-test].coca_db_test.modular_pricing definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.modular_pricing;

CREATE TABLE [backend-db-test].coca_db_test.modular_pricing (
	id bigint DEFAULT NEXT VALUE FOR [module_pricing_seq] NOT NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	module_id bigint NOT NULL,
	fp_nte_price decimal(20,8) NULL,
	dbii_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_MODULAR_PRICING PRIMARY KEY (id),
	CONSTRAINT uc_modular_pricing_quotation_id_workscope_id_module_id UNIQUE (quotation_id,workscope_id,module_id)
);


-- [backend-db-test].coca_db_test.navigation_items definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.navigation_items;

CREATE TABLE [backend-db-test].coca_db_test.navigation_items (
	id bigint DEFAULT NEXT VALUE FOR [navigation_items_seq] NOT NULL,
	is_valid bit NULL,
	quotation_id bigint NOT NULL,
	navigation_step_id bigint NOT NULL,
	progress_step_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	navigation_step_position int NOT NULL,
	progress_step_position int NOT NULL,
	CONSTRAINT PK_NAVIGATION_ITEMS PRIMARY KEY (id),
	CONSTRAINT uc_navigation_items_quotation_id_navigation_step_id_progress_step_id UNIQUE (quotation_id,navigation_step_id,progress_step_id)
);


-- [backend-db-test].coca_db_test.pma_ratings definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.pma_ratings;

CREATE TABLE [backend-db-test].coca_db_test.pma_ratings (
	id bigint DEFAULT NEXT VALUE FOR [pma_ratings_seq] NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	pma_rating_percentage decimal(4,3) NULL,
	material_pricing_item_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_PMA_RATINGS PRIMARY KEY (id),
	CONSTRAINT uc_pma_ratings_year_material_pricing_item_id UNIQUE ([year],material_pricing_item_id)
);


-- [backend-db-test].coca_db_test.production_costs definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.production_costs;

CREATE TABLE [backend-db-test].coca_db_test.production_costs (
	id bigint DEFAULT NEXT VALUE FOR [production_costs_seq] NOT NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	currency varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	material_a_part_oem decimal(20,8) NULL,
	material_a_part_surplus decimal(20,8) NULL,
	material_a_part_pma decimal(20,8) NULL,
	material_a_part_csm decimal(20,8) NULL,
	material_case_and_frame_oem decimal(20,8) NULL,
	material_case_and_frame_surplus decimal(20,8) NULL,
	material_case_and_frame_pma decimal(20,8) NULL,
	material_case_and_frame_csm decimal(20,8) NULL,
	material_llp_oem decimal(20,8) NULL,
	material_llp_surplus decimal(20,8) NULL,
	material_llp_pma decimal(20,8) NULL,
	material_llp_csm decimal(20,8) NULL,
	material_parts_package_oem decimal(20,8) NULL,
	material_parts_package_surplus decimal(20,8) NULL,
	material_parts_package_pma decimal(20,8) NULL,
	material_kits_oem decimal(20,8) NULL,
	material_kits_surplus decimal(20,8) NULL,
	material_kits_pma decimal(20,8) NULL,
	material_components_oem decimal(20,8) NULL,
	material_components_surplus decimal(20,8) NULL,
	material_components_pma decimal(20,8) NULL,
	material_non_routine_a_part decimal(20,8) NULL,
	material_non_routine_case_and_frame decimal(20,8) NULL,
	material_non_routine_component decimal(20,8) NULL,
	material_non_routine_llp decimal(20,8) NULL,
	material_routine decimal(20,8) NULL,
	subcontract_a_parts decimal(20,8) NULL,
	subcontract_case_and_frame decimal(20,8) NULL,
	subcontract_llp decimal(20,8) NULL,
	subcontract_components decimal(20,8) NULL,
	subcontract_part_packages decimal(20,8) NULL,
	labour decimal(20,8) NULL,
	total decimal(20,8) NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	calculation_status varchar(8) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT 'INCLUDED' NOT NULL,
	material_a_part_oem_cl decimal(20,8) NULL,
	material_case_and_frame_oem_cl decimal(20,8) NULL,
	material_llp_oem_cl decimal(20,8) NULL,
	material_parts_package_oem_cl decimal(20,8) NULL,
	material_kits_oem_cl decimal(20,8) NULL,
	material_components_oem_cl decimal(20,8) NULL,
	material_non_routine_a_part_cl decimal(20,8) NULL,
	material_non_routine_case_and_frame_cl decimal(20,8) NULL,
	material_non_routine_component_cl decimal(20,8) NULL,
	material_non_routine_llp_cl decimal(20,8) NULL,
	material_routine_cl decimal(20,8) NULL,
	labour_non_routine decimal(20,8) NULL,
	rlfp_engine decimal(20,8) NULL,
	rlfp_module decimal(20,8) NULL,
	cleaning_and_inspection_rlfp_engine decimal(20,8) NULL,
	cleaning_and_inspection_rlfp_module decimal(20,8) NULL,
	CONSTRAINT PK_PRODUCTION_COSTS PRIMARY KEY (id),
	CONSTRAINT uc_production_costs_year_quotation_id_workscope_id_currency_calculation_status UNIQUE ([year],quotation_id,workscope_id,currency,calculation_status)
);


-- [backend-db-test].coca_db_test.quotation_engine_workscope_classes definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.quotation_engine_workscope_classes;

CREATE TABLE [backend-db-test].coca_db_test.quotation_engine_workscope_classes (
	id bigint DEFAULT NEXT VALUE FOR [quotation_engine_workscope_classes_seq] NOT NULL,
	quotation_engine_workscope_id bigint NOT NULL,
	workscope_class varchar(1) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_QUOTATION_ENGINE_WORKSCOPE_CLASSES PRIMARY KEY (id),
	CONSTRAINT uc_quotation_engine_workscope_classes_quotation_engine_workscope_id_class UNIQUE (quotation_engine_workscope_id,workscope_class)
);


-- [backend-db-test].coca_db_test.quotation_engine_workscopes definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.quotation_engine_workscopes;

CREATE TABLE [backend-db-test].coca_db_test.quotation_engine_workscopes (
	id bigint DEFAULT NEXT VALUE FOR [quotation_engine_workscopes_seq] NOT NULL,
	quotation_engine_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_QUOTATION_ENGINE_WORKSCOPES PRIMARY KEY (id),
	CONSTRAINT uc_quotation_engine_workscopes_quotation_engine_id_workscope_id UNIQUE (quotation_engine_id,workscope_id)
);


-- [backend-db-test].coca_db_test.quotation_engines definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.quotation_engines;

CREATE TABLE [backend-db-test].coca_db_test.quotation_engines (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	engine_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_QUOTATION_ENGINES PRIMARY KEY (id),
	CONSTRAINT uc_quotation_engines_quotation_id_engine_id UNIQUE (quotation_id,engine_id)
);


-- [backend-db-test].coca_db_test.quotation_exchange_rates definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.quotation_exchange_rates;

CREATE TABLE [backend-db-test].coca_db_test.quotation_exchange_rates (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	current_exchange_rate_id bigint NOT NULL,
	previous_exchange_rate_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_QUOTATION_EXCHANGE_RATES PRIMARY KEY (id),
	CONSTRAINT UQ__quotatio__7841D7DAF54BDAE4 UNIQUE (quotation_id)
);


-- [backend-db-test].coca_db_test.quotation_owners definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.quotation_owners;

CREATE TABLE [backend-db-test].coca_db_test.quotation_owners (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	original_owner_id bigint NOT NULL,
	current_owner_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_QUOTATION_OWNERS PRIMARY KEY (id),
	CONSTRAINT uq_quotation_owners_current_owner_quotation_id UNIQUE (quotation_id,current_owner_id)
);


-- [backend-db-test].coca_db_test.quotation_unarchive_log definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.quotation_unarchive_log;

CREATE TABLE [backend-db-test].coca_db_test.quotation_unarchive_log (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	unarchived_at datetime2 NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_QUOTATION_UNARCHIVE_LOG PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.quotations definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.quotations;

CREATE TABLE [backend-db-test].coca_db_test.quotations (
	id bigint IDENTITY(1,1) NOT NULL,
	contract_start datetime2 NOT NULL,
	contract_end datetime2 NOT NULL,
	[position] int NOT NULL,
	version int NOT NULL,
	scenario int NOT NULL,
	engine_version varchar(20) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	status int DEFAULT 0 NOT NULL,
	status_last_updated datetime2 DEFAULT getdate() NOT NULL,
	base_year varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	deepest_workscope_id bigint NOT NULL,
	customer_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	original_quotation_id bigint NULL,
	copy_number int NULL,
	quotation_owner_id bigint NULL,
	offer_number varchar(7) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	CONSTRAINT PK_QUOTATIONS PRIMARY KEY (id),
	CONSTRAINT uc_quotations_position_version_scenario_offer_number_copy_number UNIQUE ([position],version,scenario,offer_number,copy_number)
);
 CREATE NONCLUSTERED INDEX idx_quotations ON backend-db-test.coca_db_test.quotations (  base_year ASC  , deepest_workscope_id ASC  )
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;


-- [backend-db-test].coca_db_test.realistic_customer_workscopes definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.realistic_customer_workscopes;

CREATE TABLE [backend-db-test].coca_db_test.realistic_customer_workscopes (
	id bigint IDENTITY(1,1) NOT NULL,
	realistic_quotation_engine_ws_id bigint NOT NULL,
	customer_quotation_engine_ws_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_REALISTIC_CUSTOMER_WORKSCOPES PRIMARY KEY (id),
	CONSTRAINT uc_realistic_customer_workscopes_realistic_quotation_engine_ws_id_customer_quotation_engine_ws_id UNIQUE (realistic_quotation_engine_ws_id,customer_quotation_engine_ws_id)
);


-- [backend-db-test].coca_db_test.repair_exclusions definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.repair_exclusions;

CREATE TABLE [backend-db-test].coca_db_test.repair_exclusions (
	id bigint DEFAULT NEXT VALUE FOR [repair_exclusions_seq] NOT NULL,
	is_excluded bit DEFAULT 0 NOT NULL,
	quotation_engine_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	cluster_id bigint NOT NULL,
	part_id bigint NOT NULL,
	repair_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_REPAIR_EXCLUSIONS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.revenue_caps definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.revenue_caps;

CREATE TABLE [backend-db-test].coca_db_test.revenue_caps (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	revenue_cap decimal(20,8) NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_REVENUE_CAPS PRIMARY KEY (id),
	CONSTRAINT uc_revenue_caps_quotation_id_workscope_id_year UNIQUE (quotation_id,workscope_id,[year])
);


-- [backend-db-test].coca_db_test.revenues definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.revenues;

CREATE TABLE [backend-db-test].coca_db_test.revenues (
	id bigint DEFAULT NEXT VALUE FOR [revenues_seq] NOT NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	currency varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	material_a_part_oem decimal(20,8) NULL,
	material_a_part_surplus decimal(20,8) NULL,
	material_a_part_pma decimal(20,8) NULL,
	material_a_part_csm decimal(20,8) NULL,
	material_parts_package_oem decimal(20,8) NULL,
	material_parts_package_surplus decimal(20,8) NULL,
	material_parts_package_pma decimal(20,8) NULL,
	material_kits_oem decimal(20,8) NULL,
	material_kits_surplus decimal(20,8) NULL,
	material_kits_pma decimal(20,8) NULL,
	material_components_oem decimal(20,8) NULL,
	material_components_surplus decimal(20,8) NULL,
	material_components_pma decimal(20,8) NULL,
	material_routine decimal(20,8) NULL,
	material_non_routine decimal(20,8) NULL,
	labour_routine decimal(20,8) NULL,
	labour_non_routine_epar decimal(20,8) NULL,
	labour_non_routine_repair decimal(20,8) NULL,
	labour_rlfp_engine decimal(20,8) NULL,
	labour_rlfp_module decimal(20,8) NULL,
	cleaning_and_inspection_rlfp_engine decimal(20,8) NULL,
	cleaning_and_inspection_rlfp_module decimal(20,8) NULL,
	cleaning_and_inspection decimal(20,8) NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	calculation_status varchar(8) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT 'INCLUDED' NOT NULL,
	subcontract_a_part decimal(20,8) NULL,
	subcontract_case_and_frame decimal(20,8) NULL,
	subcontract_llp decimal(20,8) NULL,
	subcontract_parts_package decimal(20,8) NULL,
	subcontract_components decimal(20,8) NULL,
	material_handling_charge decimal(20,8) NULL,
	subcontract_handling_charge decimal(20,8) NULL,
	CONSTRAINT PK_REVENUES PRIMARY KEY (id),
	CONSTRAINT uc_revenues_year_quotation_id_workscope_id_currency_calculation_status UNIQUE ([year],quotation_id,workscope_id,currency,calculation_status)
);


-- [backend-db-test].coca_db_test.revenues_modular_contracts definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.revenues_modular_contracts;

CREATE TABLE [backend-db-test].coca_db_test.revenues_modular_contracts (
	id bigint DEFAULT NEXT VALUE FOR [revenues_mc_seq] NOT NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	module_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	calculation_status varchar(8) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT 'INCLUDED' NOT NULL,
	material_a_part_oem decimal(20,8) NULL,
	material_a_part_surplus decimal(20,8) NULL,
	material_a_part_pma decimal(20,8) NULL,
	material_a_part_csm decimal(20,8) NULL,
	material_parts_package_oem decimal(20,8) NULL,
	material_parts_package_surplus decimal(20,8) NULL,
	material_parts_package_pma decimal(20,8) NULL,
	material_kits_oem decimal(20,8) NULL,
	material_kits_surplus decimal(20,8) NULL,
	material_kits_pma decimal(20,8) NULL,
	material_components_oem decimal(20,8) NULL,
	material_components_surplus decimal(20,8) NULL,
	material_components_pma decimal(20,8) NULL,
	material_routine decimal(20,8) NULL,
	material_non_routine decimal(20,8) NULL,
	labour_routine decimal(20,8) NULL,
	labour_non_routine_epar decimal(20,8) NULL,
	labour_non_routine_repair decimal(20,8) NULL,
	labour_rlfp_engine decimal(20,8) NULL,
	labour_rlfp_module decimal(20,8) NULL,
	cleaning_and_inspection_rlfp_engine decimal(20,8) NULL,
	cleaning_and_inspection_rlfp_module decimal(20,8) NULL,
	cleaning_and_inspection decimal(20,8) NULL,
	subcontract_a_part decimal(20,8) NULL,
	subcontract_case_and_frame decimal(20,8) NULL,
	subcontract_llp decimal(20,8) NULL,
	subcontract_parts_package decimal(20,8) NULL,
	subcontract_components decimal(20,8) NULL,
	material_handling_charge decimal(20,8) NULL,
	subcontract_handling_charge decimal(20,8) NULL,
	total_revenue decimal(20,8) NULL,
	final_revenue decimal(20,8) NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_REVENUES_MODULAR_CONTRACTS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.rlfp_engines definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.rlfp_engines;

CREATE TABLE [backend-db-test].coca_db_test.rlfp_engines (
	id bigint DEFAULT NEXT VALUE FOR [rlfp_engines_seq] NOT NULL,
	labour_pricing_item_id bigint NOT NULL,
	price decimal(20,8) NULL,
	dbii_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_RLFP_ENGINES PRIMARY KEY (id),
	CONSTRAINT UQ__rlfp_eng__F6674804E7861656 UNIQUE (labour_pricing_item_id)
);


-- [backend-db-test].coca_db_test.rlfp_modules definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.rlfp_modules;

CREATE TABLE [backend-db-test].coca_db_test.rlfp_modules (
	id bigint DEFAULT NEXT VALUE FOR [rlfp_modules_seq] NOT NULL,
	price decimal(20,8) NULL,
	dbii_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	labour_pricing_item_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_RLFP_MODULES PRIMARY KEY (id),
	CONSTRAINT uc_rlfp_modules_labour_pricing_item_id_workscope_id UNIQUE (labour_pricing_item_id,workscope_id)
);


-- [backend-db-test].coca_db_test.scrap_caps definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.scrap_caps;

CREATE TABLE [backend-db-test].coca_db_test.scrap_caps (
	id bigint DEFAULT NEXT VALUE FOR [scrap_caps_seq] NOT NULL,
	scrap_cap_percentage decimal(5,3) NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	material_pricing_item_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	is_overwritten bit DEFAULT 0 NOT NULL,
	CONSTRAINT PK_SCRAP_CAPS PRIMARY KEY (id),
	CONSTRAINT uc_scrap_caps_quotation_id_workscope_id_material_pricing_item_id UNIQUE (quotation_id,workscope_id,material_pricing_item_id)
);


-- [backend-db-test].coca_db_test.sse_events definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.sse_events;

CREATE TABLE [backend-db-test].coca_db_test.sse_events (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	event_type varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	progress_state varchar(50) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_SSE_EVENTS PRIMARY KEY (id)
);


-- [backend-db-test].coca_db_test.subcontract_pricing_items definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.subcontract_pricing_items;

CREATE TABLE [backend-db-test].coca_db_test.subcontract_pricing_items (
	id bigint DEFAULT NEXT VALUE FOR [subcontract_pricing_items_seq] NOT NULL,
	margin_percentage decimal(4,3) NULL,
	cap int NULL,
	quotation_engine_id bigint NOT NULL,
	cluster_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_SUBCONTRACT_PRICING_ITEMS PRIMARY KEY (id),
	CONSTRAINT uc_subcontract_pricing_items_quotation_engine_id_cluster_id UNIQUE (quotation_engine_id,cluster_id)
);


-- [backend-db-test].coca_db_test.surcharge_costs definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.surcharge_costs;

CREATE TABLE [backend-db-test].coca_db_test.surcharge_costs (
	id bigint DEFAULT NEXT VALUE FOR [surcharge_costs_seq] NOT NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	currency varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	[general] decimal(20,8) NULL,
	v2500_royalty_fee decimal(20,8) NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	calculation_status varchar(8) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT 'INCLUDED' NOT NULL,
	leap_surcharge decimal(20,8) DEFAULT 0 NOT NULL,
	CONSTRAINT PK_SURCHARGE_COSTS PRIMARY KEY (id),
	CONSTRAINT surcharge_costs_year_quotation_id_workscope_id_currency_calculation_status UNIQUE ([year],quotation_id,workscope_id,currency,calculation_status)
);


-- [backend-db-test].coca_db_test.usm_ratings definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.usm_ratings;

CREATE TABLE [backend-db-test].coca_db_test.usm_ratings (
	id bigint DEFAULT NEXT VALUE FOR [usm_ratings_seq] NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	usm_rating_percentage decimal(4,3) NULL,
	material_pricing_item_id bigint NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_USM_RATINGS PRIMARY KEY (id),
	CONSTRAINT uc_usm_ratings_year_material_pricing_item_id UNIQUE ([year],material_pricing_item_id)
);


-- [backend-db-test].coca_db_test.workscope_calculation_differences definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.workscope_calculation_differences;

CREATE TABLE [backend-db-test].coca_db_test.workscope_calculation_differences (
	id bigint DEFAULT NEXT VALUE FOR [workscope_calculation_differences_seq] NOT NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	currency varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	rlfp_engine_production_cost decimal(20,8) NULL,
	rlfp_module_production_cost decimal(20,8) NULL,
	rlfp_engine_revenue decimal(20,8) NULL,
	rlfp_module_revenue decimal(20,8) NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	rlfp_engine_ci_production_cost decimal(20,8) NULL,
	rlfp_module_ci_production_cost decimal(20,8) NULL,
	rlfp_engine_ci_revenue decimal(20,8) NULL,
	rlfp_module_ci_revenue decimal(20,8) NULL,
	routine_material_production_cost decimal(20,8) NULL,
	routine_material_revenue decimal(20,8) NULL,
	material_a_part_oem_cost decimal(20,8) NULL,
	material_a_part_surplus_cost decimal(20,8) NULL,
	material_a_part_pma_cost decimal(20,8) NULL,
	material_a_part_csm_cost decimal(20,8) NULL,
	material_case_and_frame_oem_cost decimal(20,8) NULL,
	material_case_and_frame_surplus_cost decimal(20,8) NULL,
	material_case_and_frame_pma_cost decimal(20,8) NULL,
	material_case_and_frame_csm_cost decimal(20,8) NULL,
	material_llp_oem_cost decimal(20,8) NULL,
	material_llp_surplus_cost decimal(20,8) NULL,
	material_llp_pma_cost decimal(20,8) NULL,
	material_llp_csm_cost decimal(20,8) NULL,
	material_parts_package_oem_cost decimal(20,8) NULL,
	material_parts_package_surplus_cost decimal(20,8) NULL,
	material_parts_package_pma_cost decimal(20,8) NULL,
	material_kits_oem_cost decimal(20,8) NULL,
	material_kits_surplus_cost decimal(20,8) NULL,
	material_kits_pma_cost decimal(20,8) NULL,
	material_components_oem_cost decimal(20,8) NULL,
	material_components_surplus_cost decimal(20,8) NULL,
	material_components_pma_cost decimal(20,8) NULL,
	material_a_part_oem_revenue decimal(20,8) NULL,
	material_a_part_surplus_revenue decimal(20,8) NULL,
	material_a_part_pma_revenue decimal(20,8) NULL,
	material_a_part_csm_revenue decimal(20,8) NULL,
	material_parts_package_oem_revenue decimal(20,8) NULL,
	material_parts_package_surplus_revenue decimal(20,8) NULL,
	material_parts_package_pma_revenue decimal(20,8) NULL,
	material_kits_oem_revenue decimal(20,8) NULL,
	material_kits_surplus_revenue decimal(20,8) NULL,
	material_kits_pma_revenue decimal(20,8) NULL,
	material_components_oem_revenue decimal(20,8) NULL,
	material_components_surplus_revenue decimal(20,8) NULL,
	material_components_pma_revenue decimal(20,8) NULL,
	CONSTRAINT PK_WORKSCOPE_CALCULATION_DIFFERENCES PRIMARY KEY (id),
	CONSTRAINT uc_workscope_calculation_differences_quotation_id_workscope_id_year_currency UNIQUE (quotation_id,workscope_id,[year],currency)
);


-- [backend-db-test].coca_db_test.workscope_comments definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.workscope_comments;

CREATE TABLE [backend-db-test].coca_db_test.workscope_comments (
	id bigint IDENTITY(1,1) NOT NULL,
	comment varchar(1000) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	major_module_id bigint NOT NULL,
	submodule_id bigint NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	CONSTRAINT PK_WORKSCOPE_COMMENTS PRIMARY KEY (id),
	CONSTRAINT uc_workscope_comments_quotation_id_workscope_id_major_module_id_submodule_id UNIQUE (quotation_id,workscope_id,major_module_id,submodule_id)
);


-- [backend-db-test].coca_db_test.workscope_summaries definition

-- Drop table

-- DROP TABLE [backend-db-test].coca_db_test.workscope_summaries;

CREATE TABLE [backend-db-test].coca_db_test.workscope_summaries (
	id bigint IDENTITY(1,1) NOT NULL,
	quotation_id bigint NOT NULL,
	workscope_id bigint NOT NULL,
	[year] varchar(4) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	currency varchar(3) COLLATE SQL_Latin1_General_CP1_CI_AS NOT NULL,
	production_costs decimal(20,8) NULL,
	discounts decimal(20,8) NULL,
	revenues decimal(20,8) NULL,
	surcharge_costs decimal(20,8) NULL,
	productions_costs_discounts_surcharges decimal(20,8) NULL,
	db2 varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	db2_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ebit varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	ebit_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	eat_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	net_margin_percentage varchar(30) COLLATE SQL_Latin1_General_CP1_CI_AS NULL,
	calculation_status varchar(8) COLLATE SQL_Latin1_General_CP1_CI_AS DEFAULT 'INCLUDED' NOT NULL,
	created_at datetime2 DEFAULT getdate() NOT NULL,
	updated_at datetime2 DEFAULT getdate() NOT NULL,
	revenue_cap decimal(20,8) NULL,
	overhead decimal(20,8) NULL,
	CONSTRAINT PK_WORKSCOPE_SUMMARIES PRIMARY KEY (id),
	CONSTRAINT uc_workscope_summaries_year_quotation_id_workscope_id_currency_calculation_status UNIQUE ([year],quotation_id,workscope_id,currency,calculation_status)
);


-- [backend-db-test].coca_db_test.calculation_items foreign keys

ALTER TABLE [backend-db-test].coca_db_test.calculation_items ADD CONSTRAINT fk_calculation_items_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.calculation_items ADD CONSTRAINT fk_calculation_items_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.cleaning_and_inspections foreign keys

ALTER TABLE [backend-db-test].coca_db_test.cleaning_and_inspections ADD CONSTRAINT fk_quotation_labour_pricing_input_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.clp_thresholds foreign keys

ALTER TABLE [backend-db-test].coca_db_test.clp_thresholds ADD CONSTRAINT fk_clp_thresholds_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.comments foreign keys

ALTER TABLE [backend-db-test].coca_db_test.comments ADD CONSTRAINT fk_comments_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.commitment_letter_parts foreign keys

ALTER TABLE [backend-db-test].coca_db_test.commitment_letter_parts ADD CONSTRAINT fk_commitment_letter_parts_parts FOREIGN KEY (part_id) REFERENCES [backend-db-test].coca_db_test.parts(id);
ALTER TABLE [backend-db-test].coca_db_test.commitment_letter_parts ADD CONSTRAINT fk_commitment_letter_parts_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.contract_types foreign keys

ALTER TABLE [backend-db-test].coca_db_test.contract_types ADD CONSTRAINT fk_contract_types_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.discounts foreign keys

ALTER TABLE [backend-db-test].coca_db_test.discounts ADD CONSTRAINT fk_discounts_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.discounts ADD CONSTRAINT fk_discounts_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.escalations foreign keys

ALTER TABLE [backend-db-test].coca_db_test.escalations ADD CONSTRAINT fk_escalations_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.fp_nte_prices_escalations foreign keys

ALTER TABLE [backend-db-test].coca_db_test.fp_nte_prices_escalations ADD CONSTRAINT fk_fp_nte_prices_escalations_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.handling_charge_global_caps foreign keys

ALTER TABLE [backend-db-test].coca_db_test.handling_charge_global_caps ADD CONSTRAINT fk_handling_charge_global_caps_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.labour_pricing_items foreign keys

ALTER TABLE [backend-db-test].coca_db_test.labour_pricing_items ADD CONSTRAINT fk_labour_pricing_items_clusters FOREIGN KEY (cluster_id) REFERENCES [backend-db-test].coca_db_test.clusters(id);
ALTER TABLE [backend-db-test].coca_db_test.labour_pricing_items ADD CONSTRAINT fk_labour_pricing_items_quotation_engines FOREIGN KEY (quotation_engine_id) REFERENCES [backend-db-test].coca_db_test.quotation_engines(id);


-- [backend-db-test].coca_db_test.labour_rates foreign keys

ALTER TABLE [backend-db-test].coca_db_test.labour_rates ADD CONSTRAINT fk_labour_rates_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.material_pricing_items foreign keys

ALTER TABLE [backend-db-test].coca_db_test.material_pricing_items ADD CONSTRAINT fk_material_pricing_items_clusters FOREIGN KEY (cluster_id) REFERENCES [backend-db-test].coca_db_test.clusters(id);
ALTER TABLE [backend-db-test].coca_db_test.material_pricing_items ADD CONSTRAINT fk_material_pricing_items_parts FOREIGN KEY (part_id) REFERENCES [backend-db-test].coca_db_test.parts(id);
ALTER TABLE [backend-db-test].coca_db_test.material_pricing_items ADD CONSTRAINT fk_material_pricing_items_quotation_engines FOREIGN KEY (quotation_engine_id) REFERENCES [backend-db-test].coca_db_test.quotation_engines(id);


-- [backend-db-test].coca_db_test.modular_pricing foreign keys

ALTER TABLE [backend-db-test].coca_db_test.modular_pricing ADD CONSTRAINT fk_modular_pricing_clusters FOREIGN KEY (module_id) REFERENCES [backend-db-test].coca_db_test.clusters(id);
ALTER TABLE [backend-db-test].coca_db_test.modular_pricing ADD CONSTRAINT fk_modular_pricing_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.modular_pricing ADD CONSTRAINT fk_modular_pricing_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.navigation_items foreign keys

ALTER TABLE [backend-db-test].coca_db_test.navigation_items ADD CONSTRAINT fk_navigation_items_navigation_steps FOREIGN KEY (navigation_step_id) REFERENCES [backend-db-test].coca_db_test.navigation_steps(id);
ALTER TABLE [backend-db-test].coca_db_test.navigation_items ADD CONSTRAINT fk_navigation_items_progress_steps FOREIGN KEY (progress_step_id) REFERENCES [backend-db-test].coca_db_test.progress_steps(id);
ALTER TABLE [backend-db-test].coca_db_test.navigation_items ADD CONSTRAINT fk_navigation_items_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.pma_ratings foreign keys

ALTER TABLE [backend-db-test].coca_db_test.pma_ratings ADD CONSTRAINT fk_pma_ratings_material_pricing_item_id FOREIGN KEY (material_pricing_item_id) REFERENCES [backend-db-test].coca_db_test.material_pricing_items(id);


-- [backend-db-test].coca_db_test.production_costs foreign keys

ALTER TABLE [backend-db-test].coca_db_test.production_costs ADD CONSTRAINT fk_production_costs_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.production_costs ADD CONSTRAINT fk_production_costs_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.quotation_engine_workscope_classes foreign keys

ALTER TABLE [backend-db-test].coca_db_test.quotation_engine_workscope_classes ADD CONSTRAINT fk_quotation_engine_workscope_classes_quotation_engine_workscopes FOREIGN KEY (quotation_engine_workscope_id) REFERENCES [backend-db-test].coca_db_test.quotation_engine_workscopes(id);


-- [backend-db-test].coca_db_test.quotation_engine_workscopes foreign keys

ALTER TABLE [backend-db-test].coca_db_test.quotation_engine_workscopes ADD CONSTRAINT fk_quotation_engine_workscopes_quotation_engines FOREIGN KEY (quotation_engine_id) REFERENCES [backend-db-test].coca_db_test.quotation_engines(id);
ALTER TABLE [backend-db-test].coca_db_test.quotation_engine_workscopes ADD CONSTRAINT fk_quotation_engine_workscopes_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.quotation_engines foreign keys

ALTER TABLE [backend-db-test].coca_db_test.quotation_engines ADD CONSTRAINT fk_quotation_engines_engines FOREIGN KEY (engine_id) REFERENCES [backend-db-test].coca_db_test.engines(id);
ALTER TABLE [backend-db-test].coca_db_test.quotation_engines ADD CONSTRAINT fk_quotation_engines_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.quotation_exchange_rates foreign keys

ALTER TABLE [backend-db-test].coca_db_test.quotation_exchange_rates ADD CONSTRAINT fk_currencies_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.quotation_exchange_rates ADD CONSTRAINT fk_current_exchange_rate_id FOREIGN KEY (current_exchange_rate_id) REFERENCES [backend-db-test].coca_db_test.exchange_rates(id);
ALTER TABLE [backend-db-test].coca_db_test.quotation_exchange_rates ADD CONSTRAINT fk_previous_exchange_rate_id FOREIGN KEY (previous_exchange_rate_id) REFERENCES [backend-db-test].coca_db_test.exchange_rates(id);


-- [backend-db-test].coca_db_test.quotation_owners foreign keys

ALTER TABLE [backend-db-test].coca_db_test.quotation_owners ADD CONSTRAINT fk_quotation_owners_current_users FOREIGN KEY (current_owner_id) REFERENCES [backend-db-test].coca_db_test.users(id);
ALTER TABLE [backend-db-test].coca_db_test.quotation_owners ADD CONSTRAINT fk_quotation_owners_original_users FOREIGN KEY (original_owner_id) REFERENCES [backend-db-test].coca_db_test.users(id);
ALTER TABLE [backend-db-test].coca_db_test.quotation_owners ADD CONSTRAINT fk_quotation_owners_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.quotation_unarchive_log foreign keys

ALTER TABLE [backend-db-test].coca_db_test.quotation_unarchive_log ADD CONSTRAINT quotation_unarchive_log_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.quotations foreign keys

ALTER TABLE [backend-db-test].coca_db_test.quotations ADD CONSTRAINT fk_quotations_customers FOREIGN KEY (customer_id) REFERENCES [backend-db-test].coca_db_test.customers(id);
ALTER TABLE [backend-db-test].coca_db_test.quotations ADD CONSTRAINT fk_quotations_quotation_owners FOREIGN KEY (quotation_owner_id) REFERENCES [backend-db-test].coca_db_test.quotation_owners(id);
ALTER TABLE [backend-db-test].coca_db_test.quotations ADD CONSTRAINT fk_quotations_quotations FOREIGN KEY (original_quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.quotations ADD CONSTRAINT fk_quotations_workscopes FOREIGN KEY (deepest_workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.realistic_customer_workscopes foreign keys

ALTER TABLE [backend-db-test].coca_db_test.realistic_customer_workscopes ADD CONSTRAINT fk_customer_ws_id_quotation_engine_ws_id FOREIGN KEY (customer_quotation_engine_ws_id) REFERENCES [backend-db-test].coca_db_test.quotation_engine_workscopes(id);
ALTER TABLE [backend-db-test].coca_db_test.realistic_customer_workscopes ADD CONSTRAINT fk_realistic_ws_id_quotation_engine_ws_id FOREIGN KEY (realistic_quotation_engine_ws_id) REFERENCES [backend-db-test].coca_db_test.quotation_engine_workscopes(id);


-- [backend-db-test].coca_db_test.repair_exclusions foreign keys

ALTER TABLE [backend-db-test].coca_db_test.repair_exclusions ADD CONSTRAINT fk_repair_exclusions_clusters FOREIGN KEY (cluster_id) REFERENCES [backend-db-test].coca_db_test.clusters(id);
ALTER TABLE [backend-db-test].coca_db_test.repair_exclusions ADD CONSTRAINT fk_repair_exclusions_parts FOREIGN KEY (part_id) REFERENCES [backend-db-test].coca_db_test.parts(id);
ALTER TABLE [backend-db-test].coca_db_test.repair_exclusions ADD CONSTRAINT fk_repair_exclusions_quotation_engines FOREIGN KEY (quotation_engine_id) REFERENCES [backend-db-test].coca_db_test.quotation_engines(id);
ALTER TABLE [backend-db-test].coca_db_test.repair_exclusions ADD CONSTRAINT fk_repair_exclusions_repairs FOREIGN KEY (repair_id) REFERENCES [backend-db-test].coca_db_test.repairs(id);
ALTER TABLE [backend-db-test].coca_db_test.repair_exclusions ADD CONSTRAINT fk_repair_exclusions_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.revenue_caps foreign keys

ALTER TABLE [backend-db-test].coca_db_test.revenue_caps ADD CONSTRAINT fk_workscope_summary_revenue_caps_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.revenue_caps ADD CONSTRAINT fk_workscope_summary_revenue_caps_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.revenues foreign keys

ALTER TABLE [backend-db-test].coca_db_test.revenues ADD CONSTRAINT fk_revenues_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.revenues ADD CONSTRAINT fk_revenues_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.revenues_modular_contracts foreign keys

ALTER TABLE [backend-db-test].coca_db_test.revenues_modular_contracts ADD CONSTRAINT fk_revenues_modular_contracts_clusters FOREIGN KEY (module_id) REFERENCES [backend-db-test].coca_db_test.clusters(id);
ALTER TABLE [backend-db-test].coca_db_test.revenues_modular_contracts ADD CONSTRAINT fk_revenues_modular_contracts_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.revenues_modular_contracts ADD CONSTRAINT fk_revenues_modular_contracts_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.rlfp_engines foreign keys

ALTER TABLE [backend-db-test].coca_db_test.rlfp_engines ADD CONSTRAINT fk_rlfp_engines_labour_pricing_items FOREIGN KEY (labour_pricing_item_id) REFERENCES [backend-db-test].coca_db_test.labour_pricing_items(id);


-- [backend-db-test].coca_db_test.rlfp_modules foreign keys

ALTER TABLE [backend-db-test].coca_db_test.rlfp_modules ADD CONSTRAINT fk_rlfp_modules_labour_pricing_items FOREIGN KEY (labour_pricing_item_id) REFERENCES [backend-db-test].coca_db_test.labour_pricing_items(id);
ALTER TABLE [backend-db-test].coca_db_test.rlfp_modules ADD CONSTRAINT fk_rlfp_modules_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.scrap_caps foreign keys

ALTER TABLE [backend-db-test].coca_db_test.scrap_caps ADD CONSTRAINT fk_scrap_caps_material_pricing_items FOREIGN KEY (material_pricing_item_id) REFERENCES [backend-db-test].coca_db_test.material_pricing_items(id);
ALTER TABLE [backend-db-test].coca_db_test.scrap_caps ADD CONSTRAINT fk_scrap_caps_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.scrap_caps ADD CONSTRAINT fk_scrap_caps_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.sse_events foreign keys

ALTER TABLE [backend-db-test].coca_db_test.sse_events ADD CONSTRAINT sse_events_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);


-- [backend-db-test].coca_db_test.subcontract_pricing_items foreign keys

ALTER TABLE [backend-db-test].coca_db_test.subcontract_pricing_items ADD CONSTRAINT fk_subcontract_pricing_items_clusters FOREIGN KEY (cluster_id) REFERENCES [backend-db-test].coca_db_test.clusters(id);
ALTER TABLE [backend-db-test].coca_db_test.subcontract_pricing_items ADD CONSTRAINT fk_subcontract_pricing_items_quotation_engines FOREIGN KEY (quotation_engine_id) REFERENCES [backend-db-test].coca_db_test.quotation_engines(id);


-- [backend-db-test].coca_db_test.surcharge_costs foreign keys

ALTER TABLE [backend-db-test].coca_db_test.surcharge_costs ADD CONSTRAINT fk_surcharge_costs_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.surcharge_costs ADD CONSTRAINT fk_surcharge_costs_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.usm_ratings foreign keys

ALTER TABLE [backend-db-test].coca_db_test.usm_ratings ADD CONSTRAINT fk_usm_ratings_material_pricing_item_id FOREIGN KEY (material_pricing_item_id) REFERENCES [backend-db-test].coca_db_test.material_pricing_items(id);


-- [backend-db-test].coca_db_test.workscope_calculation_differences foreign keys

ALTER TABLE [backend-db-test].coca_db_test.workscope_calculation_differences ADD CONSTRAINT fk_workscope_calculation_differences_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.workscope_calculation_differences ADD CONSTRAINT fk_workscope_calculation_differences_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.workscope_comments foreign keys

ALTER TABLE [backend-db-test].coca_db_test.workscope_comments ADD CONSTRAINT fk_clusters FOREIGN KEY (major_module_id) REFERENCES [backend-db-test].coca_db_test.clusters(id);
ALTER TABLE [backend-db-test].coca_db_test.workscope_comments ADD CONSTRAINT fk_workscope_comments_cluster FOREIGN KEY (submodule_id) REFERENCES [backend-db-test].coca_db_test.clusters(id);
ALTER TABLE [backend-db-test].coca_db_test.workscope_comments ADD CONSTRAINT fk_workscope_comments_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.workscope_comments ADD CONSTRAINT fk_workscope_comments_workscope FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);


-- [backend-db-test].coca_db_test.workscope_summaries foreign keys

ALTER TABLE [backend-db-test].coca_db_test.workscope_summaries ADD CONSTRAINT fk_workscope_summaries_quotations FOREIGN KEY (quotation_id) REFERENCES [backend-db-test].coca_db_test.quotations(id);
ALTER TABLE [backend-db-test].coca_db_test.workscope_summaries ADD CONSTRAINT fk_workscope_summaries_workscopes FOREIGN KEY (workscope_id) REFERENCES [backend-db-test].coca_db_test.workscopes(id);