-- Delete dependent records in correct order of FK constraints

-- Step 1: Delete from tables that reference quotation_exchange_rates, quotation_engines, etc.
DELETE FROM quotation_exchange_rates
WHERE quotation_id > 6;

DELETE FROM workscope_comments
WHERE quotation_id > 6;

DELETE FROM comments
WHERE quotation_id > 6;

-- Step 2: Delete from tables that reference quotation_engine_workscopes
DELETE FROM quotation_engine_workscope_classes
WHERE quotation_engine_workscope_id IN (
    SELECT id FROM quotation_engine_workscopes
    WHERE quotation_engine_id IN (
        SELECT id FROM quotation_engines
        WHERE quotation_id > 6
    )
);

DELETE FROM realistic_customer_workscopes
WHERE realistic_quotation_engine_ws_id IN (
    SELECT id FROM quotation_engine_workscopes
    WHERE quotation_engine_id IN (
        SELECT id FROM quotation_engines
        WHERE quotation_id > 6
    )
);

-- Step 3: Delete from quotation_engine_workscopes
DELETE FROM quotation_engine_workscopes
WHERE quotation_engine_id IN (
    SELECT id FROM quotation_engines
    WHERE quotation_id > 6
);

-- Step 4: Delete from tables that reference quotation_engines
DELETE FROM material_pricing_items
WHERE quotation_engine_id IN (
    SELECT id FROM quotation_engines
    WHERE quotation_id > 6
);

DELETE FROM labour_pricing_items
WHERE quotation_engine_id IN (
    SELECT id FROM quotation_engines
    WHERE quotation_id > 6
);

DELETE FROM subcontract_pricing_items
WHERE quotation_engine_id IN (
    SELECT id FROM quotation_engines
    WHERE quotation_id > 6
);

-- Step 5: Delete from quotation_engines
DELETE FROM quotation_engines
WHERE quotation_id > 6;

-- Step 6: Delete from tables that directly reference quotations (not already deleted in delete_input_tables.sql)
DELETE FROM calculation_items
WHERE quotation_id > 6;

-- Note: The following tables are already deleted in delete_input_tables.sql:
-- cleaning_and_inspections, clp_thresholds, contract_types, escalations,
-- fp_nte_prices_escalations, handling_charge_global_caps, commitment_letter_parts,
-- labour_rates, modular_pricing, navigation_items, scrap_caps

-- Delete from tables that are NOT handled in delete_input_tables.sql
DELETE FROM discounts
WHERE quotation_id > 6;

DELETE FROM production_costs
WHERE quotation_id > 6;

DELETE FROM quotation_unarchive_log
WHERE quotation_id > 6;

DELETE FROM revenue_caps
WHERE quotation_id > 6;

DELETE FROM revenues
WHERE quotation_id > 6;

DELETE FROM revenues_modular_contracts
WHERE quotation_id > 6;

DELETE FROM sse_events
WHERE quotation_id > 6;

DELETE FROM surcharge_costs
WHERE quotation_id > 6;

DELETE FROM workscope_calculation_differences
WHERE quotation_id > 6;

DELETE FROM workscope_summaries
WHERE quotation_id > 6;

-- Step 7: Handle circular dependency between quotations and quotation_owners
-- First, set quotation_owner_id to NULL for quotations > 6 to break the circular reference
UPDATE quotations
SET quotation_owner_id = NULL
WHERE id > 6;

-- Now delete from quotation_owners
DELETE FROM quotation_owners
WHERE quotation_id > 6;

-- Step 8: Handle self-referencing foreign key - delete child quotations first
DELETE FROM quotations
WHERE original_quotation_id > 6;

-- Step 9: Delete from quotations (finally, after all dependencies cleared)
DELETE FROM quotations
WHERE id > 6;