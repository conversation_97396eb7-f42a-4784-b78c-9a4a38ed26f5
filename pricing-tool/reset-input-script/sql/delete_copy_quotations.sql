-- Delete dependent records in correct order of FK constraints

-- Step 1: Delete from quotation_exchange_rates
DELETE FROM quotation_exchange_rates
WHERE quotation_id > 6;

-- Step 2: Delete from comments
DELETE FROM comments
WHERE quotation_id > 6;

-- Step 3: Delete from quotation_engine_workscope_classes
DELETE FROM quotation_engine_workscope_classes
WHERE quotation_engine_workscope_id IN (
    SELECT id FROM quotation_engine_workscopes
    WHERE quotation_engine_id IN (
        SELECT id FROM quotation_engines
        WHERE quotation_id > 6
    )
);

-- Step 4: Delete from realistic_customer_workscopes
DELETE FROM realistic_customer_workscopes
WHERE realistic_quotation_engine_ws_id IN (
    SELECT id FROM quotation_engine_workscopes
    WHERE quotation_engine_id IN (
        SELECT id FROM quotation_engines
        WHERE quotation_id > 6
    )
);

-- Step 5: Delete from quotation_engine_workscopes
DELETE FROM quotation_engine_workscopes
WHERE quotation_engine_id IN (
    SELECT id FROM quotation_engines
    WHERE quotation_id > 6
);

-- Step 6: Delete from material_pricing_items
DELETE FROM material_pricing_items
WHERE quotation_engine_id IN (
    SELECT id FROM quotation_engines
    WHERE quotation_id > 6
);

-- Step 7: Delete from labour_pricing_items
DELETE FROM labour_pricing_items
WHERE quotation_engine_id IN (
    SELECT id FROM quotation_engines
    WHERE quotation_id > 6
);

-- Step 8: Delete from subcontract_pricing_items
DELETE FROM subcontract_pricing_items
WHERE quotation_engine_id IN (
    SELECT id FROM quotation_engines
    WHERE quotation_id > 6
);

-- Step 9: Delete from quotation_engines
DELETE FROM quotation_engines
WHERE quotation_id > 6;

-- Step 10: Delete from calculation_items
DELETE FROM calculation_items
WHERE quotation_id > 6;

-- Step 11: Delete from handling_charge_global_caps
DELETE FROM handling_charge_global_caps
WHERE quotation_id > 6;

-- Step 12: Delete from commitment_letter_parts
DELETE FROM commitment_letter_parts
WHERE quotation_id > 6;

-- Step 13: Delete from fp_nte_prices_escalations
DELETE FROM fp_nte_prices_escalations
WHERE quotation_id > 6;

-- ✅ NEW STEP: Delete from workscope_comments to avoid FK constraint error
DELETE FROM workscope_comments
WHERE quotation_id > 6;

-- Step 14: Delete from quotations (finally, after all dependencies cleared)
DELETE FROM quotations
WHERE id > 6;